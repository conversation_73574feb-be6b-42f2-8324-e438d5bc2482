import React, { useState, useEffect } from 'react';
import { Form, Input, Select, message, Drawer, Button } from 'antd';
import { ImageUploader } from '@components/common';
import { debounce } from 'lodash';
import { communityApi, opApi, searchApi } from '@app/api';

const { TextArea } = Input;
const { Option } = Select;

interface CreateGroupChatProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  form: any;
  editData?: any; // ✅ 添加编辑数据属性
}

interface FormData {
  group_name: string; // ✅ 更新字段名以匹配接口文档
  logo_url: string; // ✅ 更新字段名以匹配接口文档
  description: string; // ✅ 更新字段名以匹配接口文档
  owner_account_id: string; // ✅ 更新字段名以匹配接口文档
  relation_id: string; // ✅ 更新字段名以匹配接口文档
}

interface CircleOption {
  id: string;
  name: string;
}

const CreateGroupChat: React.FC<CreateGroupChatProps> = ({ visible, onCancel, onOk, form, editData }) => {
  const { getFieldDecorator, validateFields, resetFields } = form;

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [circleList, setCircleList] = useState<CircleOption[]>([]);
  const [groupNameLength, setGroupNameLength] = useState(0);
  const [descriptionLength, setDescriptionLength] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false); // ✅ 编辑模式标识

  // 作者搜索状态
  const [authorSuggestions, setAuthorSuggestions] = useState<any[]>([]);
  const [selectedAuthor, setSelectedAuthor] = useState<string>('');

  // 表单布局
  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  // 初始化圈子列表和编辑模式
  useEffect(() => {
    if (visible) {
      loadCircleList();
      // ✅ 检查是否为编辑模式
      if (editData) {
        setIsEditMode(true);
        // 设置表单初始值
        setTimeout(() => {
          form.setFieldsValue({
            group_name: editData.group_name || '',
            logo_url: editData.logo_url || '',
            description: editData.description || '',
            owner_account_id: editData.owner_account_id || '',
            relation_id: editData.relation_id || '',
          });
          setGroupNameLength(editData.group_name?.length || 0);
          setDescriptionLength(editData.description?.length || 0);
          setSelectedAuthor(editData.owner_account_id || '');
        }, 100);
      } else {
        setIsEditMode(false);
      }
    }
  }, [visible, editData, form]);

  // 加载圈子列表
  const loadCircleList = async () => {
    try {
      // ✅ 使用真实API获取圈子列表
      const res = await searchApi.getCircleList({ current: 1, size: 100, enabled: true });
      const { list = [] } = res.data as any;
      const circles = list.records?.map((item: any) => ({
        id: item.id,
        name: item.name,
      })) || [];
      setCircleList(circles);
    } catch (error) {
      console.error('加载圈子列表失败:', error);
      message.error('加载圈子列表失败');
    }
  };

  // 处理表单提交
  const handleSubmit = () => {
    validateFields((err: any, values: FormData) => {
      if (!err) {
        setLoading(true);

        // ✅ 处理数据，更新字段名以匹配接口文档
        const submitData: any = {
          group_name: values.group_name?.trim(),
          description: values.description?.trim(),
          logo_url: values.logo_url,
          owner_account_id: values.owner_account_id,
        };

        // ✅ 圈子关联处理：如果关联了圈子，添加 relation_type 字段
        if (values.relation_id) {
          submitData.relation_type = 1; // 关联类型：1-圈子
          submitData.relation_id = values.relation_id;
        }

        // ✅ 编辑/新增模式区分
        const apiCall = isEditMode
          ? opApi.editGroupChat({ ...submitData, id: editData?.id })
          : opApi.createGroupChat(submitData);

        apiCall
          .then(() => {
            setLoading(false);
            message.success(isEditMode ? '群聊编辑成功' : '群聊创建成功');
            onOk(submitData);
            handleCancel();
          })
          .catch(() => {
            setLoading(false);
            message.error(isEditMode ? '群聊编辑失败' : '群聊创建失败');
          });
      }
    });
  };

  // 处理取消
  const handleCancel = () => {
    resetFields();
    setGroupNameLength(0);
    setDescriptionLength(0);
    setSelectedAuthor('');
    setAuthorSuggestions([]);
    setIsEditMode(false); // ✅ 重置编辑模式
    onCancel();
  };

  // 群名称输入处理
  const handleGroupNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length <= 16) {
      setGroupNameLength(value.length);
    }
  };

  // 群简介输入处理
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= 100) {
      setDescriptionLength(value.length);
    }
  };

  // 自定义群名称验证器
  const validateGroupName = (_rule: any, value: string, callback: any) => {
    if (!value || !value.trim()) {
      callback('请输入群名称');
      return;
    }
    if (value.length > 16) {
      callback('群名称不能超过16个字符');
      return;
    }
    callback();
  };

  // 自定义群主验证器
  const validateGroupOwner = (_rule: any, value: any, callback: any) => {
    if (!value) {
      callback('请设置群主');
      return;
    }
    callback();
  };

  // 处理作者搜索建议
  const handleAuthorSearch = debounce((value: string) => {
    if (value) {
      // 调用接口获取作者建议列表
      communityApi
        .recommendAccount_Search({ keyword: value })
        .then((res: any) => {
          setAuthorSuggestions(res.data?.list || []);
        })
        .catch(() => {
          setAuthorSuggestions([]);
        });
    } else {
      setAuthorSuggestions([]);
    }
  }, 300);

  // 处理作者选择
  const handleAuthorChange = (value: string) => {
    setSelectedAuthor(value);
  };

  return (
    <Drawer
      title={
        isEditMode ? '编辑群聊' : '创建群聊'
      }
      visible={visible}
      onClose={handleCancel}
      width={600}
      maskClosable={false}
      destroyOnClose={true}
    >
      {/* 顶部说明文字 */}
      <div style={{
        padding: '0 8px 0',
        color: '#666',
        fontSize: '14px',
        marginBottom: '24px'
      }}>
        邀请/申请加群，均无需审批
      </div>

      <Form {...formLayout}>
        {/* 群名称 */}
        <Form.Item label="群名称" required extra={`${groupNameLength}/16`}>
          {getFieldDecorator('group_name', {
            rules: [
              {
                required: true,
                validator: validateGroupName,
              },
            ],
          })(<Input placeholder="最多16字" maxLength={16} onChange={handleGroupNameChange} />)}
        </Form.Item>

        {/* 群头像 */}
        <Form.Item label="群头像" extra="支持jpg、jpeg、png图片格式，比例1:1">
          {getFieldDecorator('logo_url')(
            <ImageUploader
              ratio={1}
              accept={['image/jpeg', 'image/png', 'image/jpg']}
              imgsize={1024}
            />
          )}
        </Form.Item>

        {/* 群简介 */}
        <Form.Item label="群简介" extra={`选填，最多100字 ${descriptionLength}/100`}>
          {getFieldDecorator('description')(
            <TextArea
              placeholder="选填，最多100字"
              maxLength={100}
              rows={3}
              onChange={handleDescriptionChange}
            />
          )}
        </Form.Item>

        {/* 群主 */}
        <Form.Item label="群主" required extra="输入昵称或小潮号查找用户">
          {getFieldDecorator('owner_account_id', {
            rules: [
              {
                required: true,
                validator: validateGroupOwner,
              },
            ],
          })(
            <Select
              value={selectedAuthor}
              onChange={handleAuthorChange}
              onSearch={handleAuthorSearch}
              placeholder="输入昵称进行搜索"
              showSearch
              allowClear={true}
              filterOption={false}
            >
              {authorSuggestions.map((d: any) => (
                <Select.Option
                  style={{
                    whiteSpace: 'pre-wrap',
                  }}
                  key={d.id}
                  value={d.id}
                >
                  {`${['潮客 - ', '潮鸣号 - ', '潮鸣号 - '][d.cert_type] || ''}${
                    d.nick_name
                  } | 小潮号：${d.chao_id}`}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>

        {/* 关联圈子 */}
        <Form.Item label="关联圈子" extra="选填，最多选择1个圈子">
          {getFieldDecorator('relation_id', {
            initialValue: '',
          })(
            <Select
              placeholder="请输入圈子名称搜索"
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) => {
                const children = option?.props?.children;
                if (typeof children === 'string') {
                  return children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }
                return false;
              }}
            >
              <Option value="">无圈子</Option>
              {circleList.map((circle) => (
                <Option key={circle.id} value={circle.id}>
                  {circle.name}
                </Option>
              ))}
            </Select>
          )}
        </Form.Item>
      </Form>

      {/* 底部按钮 */}
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          width: '100%',
          borderTop: '1px solid #e8e8e8',
          padding: '10px 16px',
          background: '#fff',
          textAlign: 'right',
          left: 0,
        }}
      >
        <Button onClick={handleCancel} style={{ marginRight: 8 }}>
          取消
        </Button>
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          确定
        </Button>
      </div>
    </Drawer>
  );
};

export default Form.create<CreateGroupChatProps>({ name: 'createGroupChat' })(CreateGroupChat);
